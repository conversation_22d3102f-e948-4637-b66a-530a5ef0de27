import pandas as pd
import requests

def fetch_weather_data(start_date, end_date):
    """
    يجمع بيانات الطقس من Open-Meteo API للفترة المطلوبة لولاية سنار، السودان.
    يمكن التوسعة لاحقاً لإضافة مصادر عالمية أخرى مثل NOAA أو Visual Crossing.
    """
    latitude = 13.5675
    longitude = 33.5679
    # مصدر Open-Meteo
    url = f"https://archive-api.open-meteo.com/v1/archive?latitude={latitude}&longitude={longitude}&start_date={start_date}&end_date={end_date}&hourly=temperature_2m,relative_humidity_2m,precipitation&timezone=auto"
    response = requests.get(url)
    data = response.json()
    df = pd.DataFrame({
        'temperature': data['hourly']['temperature_2m'],
        'humidity': data['hourly']['relative_humidity_2m'],
        'precipitation': data['hourly']['precipitation']
    })
    # حفظ البيانات في ملف CSV
    df.to_csv('weather_data.csv', index=False)
    print('تم حفظ بيانات الطقس في weather_data.csv')
    return df

# لا تدمج كود جلب ERA5 مباشرة مع كود جلب Open-Meteo في ملف واحد.
# أنشئ سكريبت منفصل (download_era5.py) لجلب بيانات ERA5، كما هو موجود في المشروع.
# احتفظ بكود collect_weather_data.py لجلب بيانات Open-Meteo فقط.

# ملاحظة: لجمع بيانات من مصادر عالمية مثل GFS, ECMWF, ICON أو صور الأقمار الصناعية،
# يمكن استخدام مكتبات مثل cdsapi (لـ ECMWF) أو requests مع APIs متخصصة.
# مثال (لـ ECMWF):
# import cdsapi
# c = cdsapi.Client()
# c.retrieve('reanalysis-era5-single-levels', {
#     'product_type': 'reanalysis',
#     'variable': ['2m_temperature', 'total_precipitation', '2m_dewpoint_temperature', '10m_u_component_of_wind', '10m_v_component_of_wind'],
#     'year': '2024',
#     'month': ['05'],
#     'day': ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10'],
#     'time': [f'{h:02d}:00' for h in range(24)],
#     'area': [13.8, 33.3, 13.0, 34.3],  # شمال, غرب, جنوب, شرق ولاية سنار
#     'format': 'netcdf'
# }, 'era5_sennar_may2024.nc')
#
# ملاحظة هامة:
# بعد إنشاء الحساب في Copernicus، يجب وضع ملف .cdsapirc في مجلد المستخدم (C:/Users/<USER>/) ويحتوي على معلومات API KEY الخاصة بك.
# يمكنك الحصول على تفاصيل ملف .cdsapirc من صفحة الحساب في Copernicus.
#
# لمصادر الأقمار الصناعية: استخدم APIs مثل Google Earth Engine أو NASA Earthdata.
# لمصادر GFS وICON: استخدم APIs مثل NOAA أو Open-Meteo أو grib2/netcdf readers.
#
# بعد تحميل البيانات، استخدم مكتبات مثل xarray أو rasterio لتحليلها ودمجها مع بياناتك.

if __name__ == "__main__":
    # مثال على الاستخدام لجمع بيانات سنار
    df = fetch_weather_data('2024-05-01', '2024-05-10')
    print(df.head())
