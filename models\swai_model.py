import numpy as np
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers

# نموذج هجين LSTM + CNN للتنبؤ الزماني والمكاني
class SWAIModel(keras.Model):
    def __init__(self, input_shape, spatial_shape, num_outputs):
        super().__init__()
        # طبقات CNN لمعالجة الصور/الخرائط
        self.cnn = keras.Sequential([
            layers.Conv2D(16, (3,3), activation='relu', input_shape=spatial_shape),
            layers.MaxPooling2D((2,2)),
            layers.Conv2D(32, (3,3), activation='relu'),
            layers.Flatten()
        ])
        # طبقات LSTM لمعالجة السلاسل الزمنية
        self.lstm = layers.LSTM(64, return_sequences=False, input_shape=input_shape)
        # دمج المخرجات
        self.concat = layers.Concatenate()
        self.dense = layers.Dense(num_outputs)

    def call(self, inputs):
        seq_input, spatial_input = inputs
        x1 = self.lstm(seq_input)
        x2 = self.cnn(spatial_input)
        x = self.concat([x1, x2])
        return self.dense(x)

# مثال على الاستخدام
if __name__ == "__main__":
    # بيانات تجريبية: (batch, time, features), (batch, height, width, channels)
    seq = np.random.rand(8, 10, 4)
    img = np.random.rand(8, 32, 32, 3)
    model = SWAIModel(input_shape=(10,4), spatial_shape=(32,32,3), num_outputs=1)
    output = model([seq, img])
    print('Output shape:', output.shape)
