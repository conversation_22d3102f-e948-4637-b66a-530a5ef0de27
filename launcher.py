import os
import sys
import subprocess
from datetime import datetime

# هذا السكريبت يقوم بتشغيل منصة سنار الذكية مباشرة كبرنامج سطح مكتب

def main():
    # تشغيل Streamlit مع لوحة القيادة مباشرة
    try:
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"تشغيل منصة سنار الذكية للتنبؤات الجوية - {now}")
        subprocess.Popen([sys.executable, '-m', 'streamlit', 'run', 'swai_dashboard.py'])
    except Exception as e:
        print("حدث خطأ أثناء تشغيل المنصة:", e)
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
