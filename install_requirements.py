#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تثبيت المتطلبات تلقائياً
"""

import subprocess
import sys

def install_package(package):
    """تثبيت مكتبة معينة"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    """تثبيت جميع المتطلبات"""
    print("🔧 بدء تثبيت المتطلبات...")
    print("=" * 40)
    
    # قائمة المكتبات الأساسية
    basic_packages = [
        "pandas>=2.0.0",
        "numpy>=1.24.0", 
        "scikit-learn>=1.3.0",
        "streamlit>=1.28.0",
        "joblib>=1.3.0"
    ]
    
    # قائمة المكتبات الاختيارية
    optional_packages = [
        "matplotlib>=3.7.0",
        "seaborn>=0.12.0"
    ]
    
    print("📦 تثبيت المكتبات الأساسية:")
    for package in basic_packages:
        print(f"  🔄 تثبيت {package}...")
        if install_package(package):
            print(f"  ✅ تم تثبيت {package}")
        else:
            print(f"  ❌ فشل تثبيت {package}")
    
    print("\n📦 تثبيت المكتبات الاختيارية:")
    for package in optional_packages:
        print(f"  🔄 تثبيت {package}...")
        if install_package(package):
            print(f"  ✅ تم تثبيت {package}")
        else:
            print(f"  ⚠️ فشل تثبيت {package} (اختياري)")
    
    print("\n" + "=" * 40)
    print("✅ انتهى تثبيت المتطلبات!")
    print("\n🚀 يمكنك الآن تشغيل التطبيق:")
    print("streamlit run app.py")

if __name__ == "__main__":
    main()
