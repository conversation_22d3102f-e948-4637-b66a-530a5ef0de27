import cdsapi

if __name__ == "__main__":
    c = cdsapi.Client()
    c.retrieve(
        'reanalysis-era5-single-levels',
        {
            'product_type': 'reanalysis',
            'variable': [
                '2m_temperature', 'total_precipitation', '2m_dewpoint_temperature',
                '10m_u_component_of_wind', '10m_v_component_of_wind'
            ],
            'year': '2024',
            'month': ['05'],
            'day': ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10'],
            'time': [f'{h:02d}:00' for h in range(24)],
            'area': [13.8, 33.3, 13.0, 34.3],  # شمال, غرب, جنوب, شرق ولاية سنار
            'format': 'netcdf'
        },
        'era5_sennar_may2024.nc'
    )
    print('تم تحميل بيانات ERA5 لمنطقة سنار بنجاح!')
