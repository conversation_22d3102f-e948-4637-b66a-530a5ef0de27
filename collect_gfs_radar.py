import os
import requests
import pandas as pd
from datetime import datetime, timedelta

# إعدادات منطقة سنار
SENNAR_LAT = 13.55
SENNAR_LON = 33.62

# 1. تحميل بيانات GFS (NOAA)
def fetch_gfs_data(lat, lon, days=10, save_path="gfs_data.csv"):
    """
    تحميل بيانات GFS من Open-Meteo API (واجهة مجانية مبسطة)
    """
    end_date = datetime.utcnow().date()
    start_date = end_date - timedelta(days=days)
    url = (
        f"https://api.open-meteo.com/v1/gfs?latitude={lat}&longitude={lon}"
        f"&start_date={start_date}&end_date={end_date}"
        f"&hourly=temperature_2m,relative_humidity_2m,precipitation,wind_speed_10m,wind_direction_10m"
        f"&timezone=auto"
    )
    print(f"Fetching GFS data from: {url}")
    resp = requests.get(url)
    if resp.status_code == 200:
        data = resp.json()
        df = pd.DataFrame(data["hourly"])
        df["source"] = "GFS"
        df.to_csv(save_path, index=False)
        print(f"GFS data saved to {save_path}")
    else:
        print("Failed to fetch GFS data.")

# 2. تحميل بيانات رادار الطقس (مثال توضيحي)
def fetch_radar_data(lat, lon, save_path="radar_data.csv"):
    """
    مثال: تحميل بيانات رادار من RainViewer API (صور/بيانات)
    هنا سنحفظ فقط رابط الصورة ووقت الرصد
    """
    # RainViewer API (مثال): https://www.rainviewer.com/api.html
    radar_url = "https://tilecache.rainviewer.com/v2/radar/nowcast/0/13/33/6/2/1_1.png"
    now = datetime.utcnow()
    df = pd.DataFrame({
        "datetime": [now],
        "radar_image_url": [radar_url],
        "lat": [lat],
        "lon": [lon],
        "source": ["Radar"]
    })
    df.to_csv(save_path, index=False)
    print(f"Radar data (example) saved to {save_path}")

if __name__ == "__main__":
    print("--- جمع بيانات GFS (NOAA) ---")
    fetch_gfs_data(SENNAR_LAT, SENNAR_LON, days=10)
    print("--- جمع بيانات رادار الطقس (مثال) ---")
    fetch_radar_data(SENNAR_LAT, SENNAR_LON)
    print("تم جمع البيانات بنجاح.")
