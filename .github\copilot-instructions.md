<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

- هذا المشروع عبارة عن نموذج ذكاء اصطناعي للتنبؤ بالطقس باستخدام بايثون.
- يجب أن يتضمن المشروع كودًا لجمع بيانات الطقس، تدريب نموذج تعلم آلي (مثل الانحدار أو الشبكات العصبية)، وواجهة بسيطة لعرض التوقعات.
- استخدم مكتبات مثل scikit-learn أو TensorFlow أو Keras للنمذجة.
- يفضل استخدام واجهة تفاعلية بسيطة (مثل Streamlit أو Tkinter) لعرض النتائج.
