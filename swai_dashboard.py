import streamlit as st
import pandas as pd
import os
import pydeck as pdk
import io
from datetime import datetime
import base64

st.set_page_config(page_title="منصة سنار الذكية للتنبؤات الجوية", layout="wide")
st.title("منصة سنار الذكية للتنبؤات الجوية (SWAI)")

# دالة مساعدة لإرجاع الوقت والتاريخ الحالي كنص
def get_now_str():
    return datetime.now().strftime('%Y-%m-%d %H:%M:%S')

st.sidebar.header("خيارات البيانات")
data_source = st.sidebar.selectbox("اختر مصدر البيانات", [
    "Open-Meteo",
    "ERA5 (ECMWF)",
    "GFS (NOAA)",
    "رادار الطقس",
    "دمج المصادر (ذكي)",
    "جميع المصادر (متعدد)"
])

# خيارات فترة التدريب
st.sidebar.header("خيارات فترة التدريب")
train_period = st.sidebar.selectbox(
    "اختر فترة التدريب:",
    ["7 أيام", "10 أيام", "شهر", "3 أشهر", "سنة"]
)

# خيارات نوع النموذج
st.sidebar.header("خيارات النماذج الذكية")
model_type = st.sidebar.selectbox(
    "اختر نوع النموذج:",
    [
        "انحدار تقليدي",
        "شبكة عصبية بسيطة (MLP)",
        "LSTM عميق",
        "CNN عميق",
        "دمج LSTM+CNN",
        "Transformer/Attention",
        "دمج جميع النماذج (Ensemble)"
    ]
)

uploaded_file = st.sidebar.file_uploader("رفع ملف بيانات إضافي (CSV أو NetCDF)", type=["csv", "nc"])

if st.sidebar.button("تحديث البيانات"):
    st.info("يرجى تشغيل سكريبت جمع البيانات المناسب أو تحميل الملف المطلوب.")

st.sidebar.header("تدريب النموذج")
if st.sidebar.button("تدريب نموذج تقليدي (انحدار)"):
    os.system("python train_model.py")
    st.success(f"تم تدريب النموذج التقليدي بنجاح!\nالتاريخ والوقت: {get_now_str()}")
if st.sidebar.button("تدريب نموذج SWAI العميق"):
    os.system("python models/train_swai.py")
    st.success(f"تم تدريب نموذج SWAI العميق بنجاح!\nالتاريخ والوقت: {get_now_str()}")

st.sidebar.header("عرض النتائج والتوقعات")
if st.sidebar.button("عرض توقعات النموذج التقليدي"):
    if os.path.exists("weather_model.pkl"):
        import joblib
        model = joblib.load("weather_model.pkl")
        st.subheader("توقع درجة الحرارة")
        humidity = st.slider("الرطوبة (%)", 0, 100, 50)
        precipitation = st.slider("كمية الأمطار (مم)", 0, 50, 0)
        X_new = pd.DataFrame({"humidity": [humidity], "precipitation": [precipitation]})
        prediction = model.predict(X_new)[0]
        st.success(f"درجة الحرارة المتوقعة: {prediction:.2f}°C")
    else:
        st.warning("يرجى تدريب النموذج أولاً.")

if st.sidebar.button("عرض توقعات SWAI العميق"):
    st.info("سيتم دعم عرض توقعات النموذج العميق قريباً (يتطلب بيانات وصور مدخلة).")

st.sidebar.header("استكشاف وتحليل البيانات")
if st.sidebar.button("عرض البيانات المتوفرة"):
    if os.path.exists("weather_data.csv"):
        df = pd.read_csv("weather_data.csv")
        st.dataframe(df.head(100))
        st.line_chart(df[[col for col in df.columns if col in ["temperature", "humidity", "precipitation"]]])
    else:
        st.warning("لا يوجد ملف weather_data.csv")

if uploaded_file is not None:
    file_ext = os.path.splitext(uploaded_file.name)[1].lower()
    if file_ext == ".csv":
        df_up = pd.read_csv(uploaded_file)
        st.success(f"تم رفع ملف CSV بنجاح: {uploaded_file.name}")
        st.dataframe(df_up.head(100))
        st.line_chart(df_up[[col for col in df_up.columns if col in ["temperature", "humidity", "precipitation"]]])
        # رسم خريطة إذا توفرت أعمدة lat/lon
        if set(['lat', 'lon']).issubset(df_up.columns):
            st.subheader("خريطة توزيع البيانات الجغرافية")
            st.map(df_up[['lat', 'lon']])
    elif file_ext == ".nc":
        import xarray as xr
        ds = xr.open_dataset(uploaded_file)
        st.success(f"تم رفع ملف NetCDF بنجاح: {uploaded_file.name}")
        st.write(ds)
        # عرض متغيرات أساسية إذا وجدت
        for var in ["t2m", "tp", "d2m", "u10", "v10"]:
            if var in ds:
                st.line_chart(ds[var].isel(time=slice(0,100)).to_pandas())
        # رسم خريطة إذا توفرت إحداثيات lat/lon
        if 'latitude' in ds and 'longitude' in ds:
            st.subheader("خريطة توزيع البيانات الجغرافية (NetCDF)")
            import numpy as np
            lats = ds['latitude'].values
            lons = ds['longitude'].values
            # مثال: رسم أول قيمة زمنية
            if 't2m' in ds:
                t2m = ds['t2m'].isel(time=0).values
                map_df = pd.DataFrame({
                    'lat': np.repeat(lats, len(lons)),
                    'lon': np.tile(lons, len(lats)),
                    'value': t2m.flatten()
                })
                st.map(map_df[['lat', 'lon']])
    else:
        st.warning("صيغة الملف غير مدعومة")

st.sidebar.header("ملاحظات")
st.sidebar.info("يمكنك جمع بيانات جديدة أو تحميل بيانات عالمية من ERA5 أو دمجها عبر السكريبتات الجاهزة.")

st.sidebar.header("النشرة الجوية اليومية")
daily_options = st.sidebar.multiselect(
    "اختر عناصر النشرة اليومية لعرضها أو تصديرها:",
    [
        "درجة الحرارة المتوقعة",
        "الرطوبة النسبية",
        "كمية الأمطار",
        "سرعة الرياح",
        "اتجاه الرياح",
        "احتمالية العواصف",
        "الأشعة فوق البنفسجية",
        "تنبيهات وتحذيرات",
        "توقعات الفترات (صباح/ظهر/مساء)",
        "تأثيرات على الزراعة",
        "تأثيرات على الصحة",
        "تأثيرات بيئية"
    ],
    default=["درجة الحرارة المتوقعة", "الرطوبة النسبية", "كمية الأمطار"]
)
if st.sidebar.button("عرض النشرة اليومية"):
    st.subheader("النشرة الجوية اليومية لولاية سنار")
    st.markdown(f"**تاريخ ووقت النشرة:** {get_now_str()}")
    # محاولة جلب بيانات فعلية من weather_data.csv
    data_lines = []
    if os.path.exists("weather_data.csv"):
        df = pd.read_csv("weather_data.csv")
        # جلب آخر صف (أحدث بيانات)
        last_row = df.tail(1).to_dict(orient="records")[0]
        for opt in daily_options:
            # ربط الخيار بالعمود المناسب إن وجد
            if "حرارة" in opt and any(x in last_row for x in ["temperature", "درجة الحرارة"]):
                val = last_row.get("temperature", last_row.get("درجة الحرارة", "-"))
                data_lines.append(f"- {opt}: {val}°C")
            elif "رطوبة" in opt and any(x in last_row for x in ["humidity", "الرطوبة"]):
                val = last_row.get("humidity", last_row.get("الرطوبة", "-"))
                data_lines.append(f"- {opt}: {val}%")
            elif "أمطار" in opt and any(x in last_row for x in ["precipitation", "rain", "precip", "كمية الأمطار"]):
                for col in ["precipitation", "rain", "precip", "كمية الأمطار"]:
                    if col in last_row:
                        val = last_row[col]
                        break
                else:
                    val = "-"
                data_lines.append(f"- {opt}: {val} مم")
            else:
                data_lines.append(f"- {opt}")
    else:
        for opt in daily_options:
            data_lines.append(f"- {opt}")
    for line in data_lines:
        st.markdown(line)
    st.info("يمكنك تخصيص عناصر النشرة وتصديرها أو طباعتها.")

if st.sidebar.button("تصدير النشرة اليومية إلى PDF"):
    from fpdf import FPDF
    pdf = FPDF()
    pdf.add_page()
    # دعم خط يدعم العربية (مثلاً DejaVu)
    font_path = "DejaVuSans.ttf"
    if os.path.exists(font_path):
        pdf.add_font('DejaVu', '', font_path, uni=True)
        pdf.set_font('DejaVu', '', 14)
    else:
        pdf.set_font("Arial", size=14)
    # تحويل النصوص إلى UTF-8 ثم decode latin-1 مع الاستبدال
    def safe_text(txt):
        try:
            return str(txt).encode("latin-1", "replace").decode("latin-1")
        except Exception:
            return "[محتوى غير مدعوم]"
    pdf.cell(200, 10, txt=safe_text("النشرة الجوية اليومية لولاية سنار"), ln=True, align='C')
    pdf.ln(10)
    for opt in daily_options:
        pdf.cell(200, 10, txt=safe_text(f"- {opt}"), ln=True, align='R')
    pdf.ln(10)
    pdf.set_font("Arial", size=10)
    date_str = f"تاريخ التصدير: {get_now_str()}"
    pdf.cell(200, 10, txt=safe_text(date_str), ln=True, align='R')
    pdf_output = io.BytesIO()
    pdf_bytes = pdf.output(dest='S').encode('latin-1')
    pdf_output.write(pdf_bytes)
    pdf_output.seek(0)
    b64 = base64.b64encode(pdf_output.read()).decode()
    href = f'<a href="data:application/pdf;base64,{b64}" download="sennar_daily_bulletin.pdf">تحميل النشرة اليومية PDF</a>'
    st.markdown(href, unsafe_allow_html=True)

# 1. دعم تصدير النشرة اليومية إلى Word
if st.sidebar.button("تصدير النشرة اليومية إلى Word"):
    from docx import Document
    doc = Document()
    doc.add_heading("النشرة الجوية اليومية لولاية سنار", 0)
    for opt in daily_options:
        doc.add_paragraph(f"- {opt}")
    doc.add_paragraph(f"تاريخ التصدير: {get_now_str()}")
    word_output = io.BytesIO()
    doc.save(word_output)
    word_output.seek(0)
    b64 = base64.b64encode(word_output.read()).decode()
    href = f'<a href="data:application/vnd.openxmlformats-officedocument.wordprocessingml.document;base64,{b64}" download="sennar_daily_bulletin.docx">تحميل النشرة اليومية Word</a>'
    st.markdown(href, unsafe_allow_html=True)

# 2. دعم تعدد اللغات (عربي/إنجليزي)
language = st.sidebar.selectbox("اختر اللغة", ["العربية", "English"])
def translate(text):
    translations = {
        "العربية": {
            "درجة الحرارة المتوقعة": "درجة الحرارة المتوقعة",
            "الرطوبة النسبية": "الرطوبة النسبية",
            "كمية الأمطار": "كمية الأمطار",
            "سرعة الرياح": "سرعة الرياح",
            "اتجاه الرياح": "اتجاه الرياح",
            "احتمالية العواصف": "احتمالية العواصف",
            "الأشعة فوق البنفسجية": "الأشعة فوق البنفسجية",
            "تنبيهات وتحذيرات": "تنبيهات وتحذيرات",
            "توقعات الفترات (صباح/ظهر/مساء)": "توقعات الفترات (صباح/ظهر/مساء)",
            "تأثيرات على الزراعة": "تأثيرات على الزراعة",
            "تأثيرات على الصحة": "تأثيرات على الصحة",
            "تأثيرات بيئية": "تأثيرات بيئية"
        },
        "English": {
            "درجة الحرارة المتوقعة": "Expected Temperature",
            "الرطوبة النسبية": "Relative Humidity",
            "كمية الأمطار": "Precipitation",
            "سرعة الرياح": "Wind Speed",
            "اتجاه الرياح": "Wind Direction",
            "احتمالية العواصف": "Storm Probability",
            "الأشعة فوق البنفسجية": "UV Index",
            "تنبيهات وتحذيرات": "Alerts & Warnings",
            "توقعات الفترات (صباح/ظهر/مساء)": "Day Periods Forecast (Morning/Noon/Evening)",
            "تأثيرات على الزراعة": "Agricultural Impact",
            "تأثيرات على الصحة": "Health Impact",
            "تأثيرات بيئية": "Environmental Impact"
        }
    }
    return translations[language].get(text, text)

# 3. دعم إضافة شعار للنشرة
logo_path = "logo.png"
if os.path.exists(logo_path):
    st.sidebar.image(logo_path, width=120)

# 4. دعم تنبيهات ذكية تلقائية
if "كمية الأمطار" in daily_options and os.path.exists("weather_data.csv"):
    df = pd.read_csv("weather_data.csv")
    precip_col = None
    for col in df.columns:
        if col.strip().lower() in ["precipitation", "rain", "precip", "كمية الأمطار"]:
            precip_col = col
            break
    if precip_col and df[precip_col].mean() > 10:
        st.warning("تنبيه: هناك احتمالية لهطول أمطار غزيرة اليوم!")

# 5. دعم تصدير النشرة اليومية تلقائياً عند اختيار اللغة
if st.sidebar.button("عرض النشرة اليومية (لغة مختارة)"):
    st.subheader(f"النشرة الجوية اليومية لولاية سنار - {language}")
    st.markdown(f"**تاريخ ووقت النشرة:** {get_now_str()}")
    for opt in daily_options:
        st.markdown(f"- {translate(opt)}")
    st.info("يمكنك تخصيص عناصر النشرة وتصديرها أو طباعتها.")
