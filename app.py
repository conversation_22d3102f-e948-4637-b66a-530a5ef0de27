import streamlit as st
import pandas as pd
import joblib
import time
import numpy as np
import os

# استخدام النسخة المبسطة التي تعمل بدون matplotlib
from train_model_simple import train_advanced_weather_model

# إعداد الصفحة
st.set_page_config(
    page_title="نظام توقع الطقس المتقدم",
    page_icon="🌤️",
    layout="wide"
)

st.title('🌤️ نظام توقع درجة الحرارة بالذكاء الاصطناعي المتقدم')
st.markdown("---")

# الشريط الجانبي للتحكم
st.sidebar.header("⚙️ إعدادات النظام")

# قسم إدارة النموذج
st.sidebar.subheader("🤖 إدارة النموذج")

if st.sidebar.button("🔄 تدريب نموذج جديد"):
    with st.spinner("جاري تدريب النموذج... قد يستغرق بضع دقائق"):
        try:
            model_info, results = train_advanced_weather_model()
            st.sidebar.success("✅ تم تدريب النموذج بنجاح!")
            st.sidebar.json({
                "اسم النموذج": model_info['model_name'],
                "دقة النموذج": f"{model_info['performance']['r2']:.4f}",
                "متوسط الخطأ المطلق": f"{model_info['performance']['mae']:.4f}"
            })
        except Exception as e:
            st.sidebar.error(f"❌ خطأ في التدريب: {str(e)}")

# التحقق من وجود النموذج
model_path = 'weather_model_advanced.pkl'
if not os.path.exists(model_path):
    st.warning("⚠️ لم يتم العثور على النموذج المتقدم. سيتم تدريب نموذج جديد...")
    with st.spinner("جاري تدريب النموذج لأول مرة..."):
        try:
            model_info, results = train_advanced_weather_model()
            st.success("✅ تم تدريب النموذج بنجاح!")
        except Exception as e:
            st.error(f"❌ خطأ في التدريب: {str(e)}")
            st.stop()

# تحميل النموذج
try:
    model_info = joblib.load(model_path)
    st.sidebar.success(f"✅ تم تحميل النموذج: {model_info['model_name']}")
    st.sidebar.metric("دقة النموذج (R²)", f"{model_info['performance']['r2']:.4f}")
except Exception as e:
    st.error(f"❌ خطأ في تحميل النموذج: {str(e)}")
    st.stop()

# واجهة الإدخال الرئيسية
st.header("📊 إدخال بيانات الطقس")

col1, col2 = st.columns(2)

with col1:
    st.subheader("🌡️ المتغيرات الأساسية")
    humidity = st.slider('الرطوبة (%)', 0, 100, 60, help="نسبة الرطوبة في الهواء")
    precipitation = st.slider('كمية الأمطار (مم)', 0.0, 50.0, 2.0, step=0.1, help="كمية الأمطار المتوقعة")

with col2:
    st.subheader("🌪️ المتغيرات المتقدمة")
    wind_speed = st.slider('سرعة الرياح (كم/س)', 0.0, 30.0, 10.0, step=0.5, help="سرعة الرياح")
    pressure = st.slider('الضغط الجوي (هكتوباسكال)', 980, 1040, 1013, help="الضغط الجوي")

# اختيار الفصل
st.subheader("🍂 الفصل")
season_options = {
    'الربيع': 'spring',
    'الصيف': 'summer',
    'الخريف': 'autumn',
    'الشتاء': 'winter'
}
selected_season_ar = st.selectbox('اختر الفصل:', list(season_options.keys()))
selected_season = season_options[selected_season_ar]

# إنشاء DataFrame للتوقع
def create_prediction_dataframe(humidity, precipitation, wind_speed, pressure, season):
    # إنشاء DataFrame أساسي
    data = {
        'humidity': [humidity],
        'precipitation': [precipitation],
        'wind_speed': [wind_speed],
        'pressure': [pressure]
    }

    # إضافة متغيرات الفصول الوهمية
    seasons = ['spring', 'summer', 'autumn', 'winter']
    for s in seasons:
        data[f'season_{s}'] = [1 if s == season else 0]

    return pd.DataFrame(data)

# زر التوقع
st.markdown("---")
if st.button('🔮 توقع درجة الحرارة', type="primary", use_container_width=True):
    start_time = time.time()

    try:
        # إنشاء البيانات للتوقع
        X_new = create_prediction_dataframe(humidity, precipitation, wind_speed, pressure, selected_season)

        # التأكد من ترتيب الأعمدة
        X_new = X_new[model_info['feature_columns']]

        # التوقع
        prediction = model_info['model'].predict(X_new)[0]
        elapsed = time.time() - start_time

        # عرض النتائج
        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric(
                label="🌡️ درجة الحرارة المتوقعة",
                value=f"{prediction:.1f}°C",
                delta=f"±{model_info['performance']['mae']:.1f}°C"
            )

        with col2:
            st.metric(
                label="⏱️ زمن التوقع",
                value=f"{elapsed*1000:.1f} مللي ثانية"
            )

        with col3:
            st.metric(
                label="🎯 دقة النموذج",
                value=f"{model_info['performance']['r2']*100:.1f}%"
            )

        # تفسير النتيجة
        st.subheader("📝 تفسير النتيجة")

        if prediction > 30:
            st.success("🔥 الطقس حار - يُنصح بارتداء ملابس خفيفة وشرب الكثير من الماء")
        elif prediction > 20:
            st.info("☀️ الطقس معتدل - مناسب للأنشطة الخارجية")
        elif prediction > 10:
            st.warning("🧥 الطقس بارد - يُنصح بارتداء ملابس دافئة")
        else:
            st.error("🥶 الطقس بارد جداً - احرص على التدفئة الجيدة")

    except Exception as e:
        st.error(f"❌ خطأ في التوقع: {str(e)}")

# معلومات إضافية
st.markdown("---")
st.subheader("ℹ️ معلومات النموذج")

col1, col2 = st.columns(2)

with col1:
    st.info(f"""
    **نوع النموذج:** {model_info['model_name']}

    **المتغيرات المستخدمة:**
    - الرطوبة (%)
    - كمية الأمطار (مم)
    - سرعة الرياح (كم/س)
    - الضغط الجوي (هكتوباسكال)
    - الفصل
    """)

with col2:
    st.success(f"""
    **أداء النموذج:**
    - دقة R²: {model_info['performance']['r2']:.4f}
    - متوسط الخطأ المطلق: {model_info['performance']['mae']:.2f}°C
    - الجذر التربيعي لمتوسط مربع الخطأ: {np.sqrt(model_info['performance']['mse']):.2f}°C
    """)
