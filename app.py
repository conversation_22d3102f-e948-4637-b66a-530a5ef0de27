import streamlit as st
import pandas as pd
import joblib
import time

st.title('توقع درجة الحرارة بالذكاء الاصطناعي')

humidity = st.slider('الرطوبة (%)', 0, 100, 50)
precipitation = st.slider('كمية الأمطار (مم)', 0, 50, 0)

if st.button('توقع درجة الحرارة'):
    start_time = time.time()
    model = joblib.load('weather_model.pkl')
    X_new = pd.DataFrame({'humidity': [humidity], 'precipitation': [precipitation]})
    prediction = model.predict(X_new)[0]
    elapsed = time.time() - start_time
    st.success(f'درجة الحرارة المتوقعة: {prediction:.2f}°C')
    st.info(f'زمن تنفيذ التوقع: {elapsed:.2f} ثانية')
