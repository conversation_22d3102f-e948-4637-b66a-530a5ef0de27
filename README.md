# مشروع نموذج ذكاء اصطناعي للتنبؤ بالطقس

هذا المشروع يستخدم لغة بايثون لبناء نموذج تعلم آلي يتنبأ بالطقس بناءً على بيانات الطقس التاريخية. يشمل المشروع:

- جمع بيانات الطقس (يمكن استخدام بيانات مفتوحة مثل OpenWeatherMap أو Kaggle)
- معالجة البيانات وتجهيزها للنموذج
- تدريب نموذج تعلم آلي (انحدار أو شبكة عصبية)
- واجهة مستخدم بسيطة لعرض التوقعات (مثلاً باستخدام Streamlit)

## المتطلبات
- Python 3.8+
- scikit-learn
- pandas
- numpy
- streamlit

## خطوات التشغيل
1. تثبيت المتطلبات:
   ```powershell
   pip install -r requirements.txt
   ```
2. تشغيل التطبيق:
   ```powershell
   streamlit run app.py
   ```

## ملاحظات
- يمكنك تعديل الكود ليتناسب مع مصادر بيانات مختلفة أو نماذج تعلم آلي أخرى.
