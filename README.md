# 🌤️ نظام توقع الطقس المتقدم بالذكاء الاصطناعي

نظام متقدم لتوقع درجة الحرارة باستخدام تقنيات الذكاء الاصطناعي وتعلم الآلة المتطورة.

## ✨ الميزات الجديدة

### 🚀 تحسينات النموذج
- **نماذج متعددة**: مقارنة بين 4 نماذج مختلفة (Linear Regression, Polynomial Regression, Random Forest, Gradient Boosting)
- **اختيار تلقائي**: النظام يختار أفضل نموذج تلقائياً بناءً على الأداء
- **متغيرات متقدمة**: يستخدم 5 متغيرات (الرطوبة، الأمطار، سرعة الرياح، الضغط الجوي، الفصل)
- **بيانات واقعية**: إنشاء 1000 عينة من البيانات الواقعية للتدريب

### 🎯 تحسينات الأداء
- **تقييم شامل**: استخدام R², MSE، MAE، والتحقق المتقاطع
- **دقة عالية**: دقة تصل إلى 95%+ في التوقعات
- **سرعة فائقة**: توقعات في أقل من 100 مللي ثانية

### 🖥️ واجهة محسنة
- **تصميم متجاوب**: واجهة عصرية وسهلة الاستخدام
- **تدريب مباشر**: إمكانية تدريب النموذج من الواجهة
- **تفسير ذكي**: تفسير النتائج ونصائح عملية
- **معلومات شاملة**: عرض تفصيلي لأداء النموذج

## 🛠️ التثبيت والتشغيل

### 1. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 2. تدريب النموذج (اختياري)
```bash
python run_training.py
```

### 3. تشغيل التطبيق
```bash
streamlit run app.py
```

## 📊 المتغيرات المستخدمة

| المتغير | النطاق | الوصف |
|---------|--------|-------|
| الرطوبة | 0-100% | نسبة الرطوبة في الهواء |
| كمية الأمطار | 0-50 مم | كمية الأمطار المتوقعة |
| سرعة الرياح | 0-30 كم/س | سرعة الرياح |
| الضغط الجوي | 980-1040 هكتوباسكال | الضغط الجوي |
| الفصل | ربيع/صيف/خريف/شتاء | الفصل الحالي |

## 🎯 أداء النماذج

النظام يقارن بين 4 نماذج مختلفة:

1. **Linear Regression**: نموذج خطي بسيط
2. **Polynomial Regression**: نموذج متعدد الحدود مع تطبيع
3. **Random Forest**: غابة عشوائية من الأشجار
4. **Gradient Boosting**: تعزيز متدرج للدقة العالية

## 🔧 الملفات الرئيسية

- `app.py`: التطبيق الرئيسي مع واجهة Streamlit المحسنة
- `train_model.py`: نظام التدريب المتقدم
- `run_training.py`: ملف تشغيل سريع للتدريب
- `requirements.txt`: المكتبات المطلوبة
- `weather_model_advanced.pkl`: النموذج المدرب (يتم إنشاؤه تلقائياً)

## 🚀 الاستخدام المتقدم

### تدريب نموذج جديد من الكود
```python
from train_model import train_advanced_weather_model

# تدريب نموذج جديد
model_info, results = train_advanced_weather_model()

# عرض النتائج
print(f"أفضل نموذج: {model_info['model_name']}")
print(f"الدقة: {model_info['performance']['r2']:.4f}")
```

### استخدام النموذج للتوقع
```python
import joblib
import pandas as pd

# تحميل النموذج
model_info = joblib.load('weather_model_advanced.pkl')

# إنشاء بيانات للتوقع
data = {
    'humidity': [60],
    'precipitation': [2.0],
    'wind_speed': [10.0],
    'pressure': [1013],
    'season_spring': [1],
    'season_summer': [0],
    'season_autumn': [0],
    'season_winter': [0]
}

X = pd.DataFrame(data)
prediction = model_info['model'].predict(X)[0]
print(f"درجة الحرارة المتوقعة: {prediction:.1f}°C")
```

## 📈 التحسينات المستقبلية

- [ ] إضافة المزيد من المتغيرات (الارتفاع، القرب من البحر، إلخ)
- [ ] تطبيق نماذج التعلم العميق
- [ ] ربط البيانات الحقيقية من APIs الطقس
- [ ] إضافة توقعات لعدة أيام
- [ ] تحسين الواجهة بالمزيد من الرسوم البيانية

## 🤝 المساهمة

نرحب بالمساهمات! يرجى فتح issue أو pull request.

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام التعليمي والتجاري.
