{"version": "2.0.0", "tasks": [{"type": "shell", "label": "Install Python requirements", "command": "pip install -r requirements.txt", "group": "build", "problemMatcher": []}, {"label": "Train Weather Model", "type": "shell", "command": "python train_model.py", "group": "build", "problemMatcher": []}, {"label": "Run Weather App", "type": "shell", "command": "streamlit run app.py", "group": "test", "problemMatcher": []}]}