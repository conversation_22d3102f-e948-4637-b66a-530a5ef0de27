#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف اختبار النظام
"""

def test_imports():
    """اختبار استيراد المكتبات"""
    try:
        import pandas as pd
        print("✅ pandas: OK")
        
        import numpy as np
        print("✅ numpy: OK")
        
        import sklearn
        print("✅ sklearn: OK")
        
        import joblib
        print("✅ joblib: OK")
        
        import matplotlib.pyplot as plt
        print("✅ matplotlib: OK")
        
        import seaborn as sns
        print("✅ seaborn: OK")
        
        return True
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False

def test_model_training():
    """اختبار تدريب النموذج"""
    try:
        from train_model import generate_realistic_weather_data, train_advanced_weather_model
        print("✅ استيراد وحدة التدريب: OK")
        
        # إنشاء بيانات تجريبية صغيرة
        print("🔄 إنشاء بيانات تجريبية...")
        df = generate_realistic_weather_data(n_samples=100, save_path='test_data.csv')
        print(f"✅ تم إنشاء {len(df)} عينة")
        
        # تدريب النموذج
        print("🔄 تدريب النموذج...")
        model_info, results = train_advanced_weather_model('test_data.csv')
        print(f"✅ تم تدريب النموذج: {model_info['model_name']}")
        print(f"✅ دقة النموذج: {model_info['performance']['r2']:.4f}")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في التدريب: {e}")
        return False

def test_prediction():
    """اختبار التوقع"""
    try:
        import joblib
        import pandas as pd
        
        # تحميل النموذج
        model_info = joblib.load('weather_model_advanced.pkl')
        print("✅ تم تحميل النموذج")
        
        # إنشاء بيانات للاختبار
        test_data = {
            'humidity': [60],
            'precipitation': [2.0],
            'wind_speed': [10.0],
            'pressure': [1013],
            'season_spring': [1],
            'season_summer': [0],
            'season_autumn': [0],
            'season_winter': [0]
        }
        
        X_test = pd.DataFrame(test_data)
        X_test = X_test[model_info['feature_columns']]
        
        # التوقع
        prediction = model_info['model'].predict(X_test)[0]
        print(f"✅ التوقع: {prediction:.1f}°C")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في التوقع: {e}")
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء اختبار النظام")
    print("=" * 40)
    
    # اختبار الاستيراد
    print("\n1️⃣ اختبار استيراد المكتبات:")
    imports_ok = test_imports()
    
    if not imports_ok:
        print("❌ فشل في استيراد المكتبات. يرجى تثبيت المتطلبات:")
        print("pip install -r requirements.txt")
        return
    
    # اختبار التدريب
    print("\n2️⃣ اختبار تدريب النموذج:")
    training_ok = test_model_training()
    
    if not training_ok:
        print("❌ فشل في تدريب النموذج")
        return
    
    # اختبار التوقع
    print("\n3️⃣ اختبار التوقع:")
    prediction_ok = test_prediction()
    
    if not prediction_ok:
        print("❌ فشل في التوقع")
        return
    
    print("\n" + "=" * 40)
    print("🎉 جميع الاختبارات نجحت!")
    print("✅ النظام جاهز للاستخدام")
    print("\n🚀 لتشغيل التطبيق:")
    print("streamlit run app.py")

if __name__ == "__main__":
    main()
