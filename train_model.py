import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression
import joblib

def train_weather_model(data_path):
    """
    يدرب نموذج انحدار خطي للتنبؤ بدرجة الحرارة.
    """
    df = pd.read_csv(data_path)
    X = df[['humidity', 'precipitation']]
    y = df['temperature']
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    model = LinearRegression()
    model.fit(X_train, y_train)
    joblib.dump(model, 'weather_model.pkl')
    print(f"تم حفظ النموذج في weather_model.pkl. الدقة: {model.score(X_test, y_test):.2f}")

if __name__ == "__main__":
    # مثال على الاستخدام
    train_weather_model('weather_data.csv')
