import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.preprocessing import StandardScaler, PolynomialFeatures
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.pipeline import Pipeline
import joblib
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

def generate_realistic_weather_data(n_samples=1000, save_path='weather_data_enhanced.csv'):
    """
    ينشئ بيانات طقس واقعية للتدريب
    """
    np.random.seed(42)

    # إنشاء بيانات أساسية
    humidity = np.random.normal(60, 20, n_samples)  # رطوبة بمتوسط 60%
    humidity = np.clip(humidity, 10, 100)

    precipitation = np.random.exponential(2, n_samples)  # أمطار بتوزيع أسي
    precipitation = np.clip(precipitation, 0, 50)

    wind_speed = np.random.gamma(2, 3, n_samples)  # سرعة الرياح
    wind_speed = np.clip(wind_speed, 0, 30)

    pressure = np.random.normal(1013, 15, n_samples)  # الضغط الجوي
    pressure = np.clip(pressure, 980, 1040)

    # إنشاء درجة الحرارة بناءً على العوامل الأخرى
    temperature = (
        35 - 0.15 * humidity +  # كلما زادت الرطوبة قلت الحرارة
        -0.8 * precipitation +  # الأمطار تقلل الحرارة
        -0.2 * wind_speed +     # الرياح تقلل الحرارة
        0.02 * (pressure - 1013) +  # الضغط يؤثر قليلاً
        np.random.normal(0, 3, n_samples)  # ضوضاء عشوائية
    )

    # إضافة تأثيرات موسمية
    season = np.random.choice(['spring', 'summer', 'autumn', 'winter'], n_samples)
    season_effect = {'spring': 2, 'summer': 8, 'autumn': -2, 'winter': -8}
    temperature += [season_effect[s] for s in season]

    # إنشاء DataFrame
    df = pd.DataFrame({
        'humidity': humidity,
        'precipitation': precipitation,
        'wind_speed': wind_speed,
        'pressure': pressure,
        'season': season,
        'temperature': temperature
    })

    # تحويل الفصول إلى متغيرات وهمية
    df = pd.get_dummies(df, columns=['season'], prefix='season')

    df.to_csv(save_path, index=False)
    print(f"تم إنشاء {n_samples} عينة من البيانات وحفظها في {save_path}")
    return df

def train_advanced_weather_model(data_path='weather_data_enhanced.csv'):
    """
    يدرب نماذج متقدمة متعددة للتنبؤ بدرجة الحرارة
    """
    print("🔄 بدء تدريب النماذج المتقدمة...")

    # قراءة البيانات
    if data_path == 'weather_data_enhanced.csv':
        df = generate_realistic_weather_data()
    else:
        df = pd.read_csv(data_path)

    # تحضير البيانات
    feature_columns = [col for col in df.columns if col != 'temperature']
    X = df[feature_columns]
    y = df['temperature']

    print(f"📊 عدد العينات: {len(df)}")
    print(f"📊 عدد الميزات: {len(feature_columns)}")
    print(f"📊 الميزات: {feature_columns}")

    # تقسيم البيانات
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

    # النماذج المختلفة للمقارنة
    models = {
        'Linear Regression': LinearRegression(),
        'Polynomial Regression': Pipeline([
            ('scaler', StandardScaler()),
            ('poly', PolynomialFeatures(degree=2)),
            ('linear', LinearRegression())
        ]),
        'Random Forest': RandomForestRegressor(n_estimators=100, random_state=42),
        'Gradient Boosting': GradientBoostingRegressor(n_estimators=100, random_state=42)
    }

    best_model = None
    best_score = -float('inf')
    results = {}

    print("\n🧪 تدريب وتقييم النماذج:")
    print("-" * 60)

    for name, model in models.items():
        # التدريب
        model.fit(X_train, y_train)

        # التوقع
        y_pred = model.predict(X_test)

        # التقييم
        r2 = r2_score(y_test, y_pred)
        mse = mean_squared_error(y_test, y_pred)
        mae = mean_absolute_error(y_test, y_pred)

        # التحقق المتقاطع
        cv_scores = cross_val_score(model, X_train, y_train, cv=5, scoring='r2')

        results[name] = {
            'model': model,
            'r2': r2,
            'mse': mse,
            'mae': mae,
            'cv_mean': cv_scores.mean(),
            'cv_std': cv_scores.std()
        }

        print(f"{name}:")
        print(f"  R² Score: {r2:.4f}")
        print(f"  MSE: {mse:.4f}")
        print(f"  MAE: {mae:.4f}")
        print(f"  CV Score: {cv_scores.mean():.4f} (±{cv_scores.std():.4f})")
        print()

        if r2 > best_score:
            best_score = r2
            best_model = model
            best_name = name

    print(f"🏆 أفضل نموذج: {best_name} بدقة {best_score:.4f}")

    # حفظ أفضل نموذج
    model_info = {
        'model': best_model,
        'feature_columns': feature_columns,
        'model_name': best_name,
        'performance': results[best_name]
    }

    joblib.dump(model_info, 'weather_model_advanced.pkl')
    print(f"✅ تم حفظ النموذج في weather_model_advanced.pkl")

    return model_info, results

if __name__ == "__main__":
    model_info, results = train_advanced_weather_model()
