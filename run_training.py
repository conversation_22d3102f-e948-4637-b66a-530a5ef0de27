#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل سريع لتدريب النموذج
"""

from train_model import train_advanced_weather_model
import sys

def main():
    """
    تشغيل تدريب النموذج المتقدم
    """
    print("🚀 بدء تدريب نموذج توقع الطقس المتقدم")
    print("=" * 50)
    
    try:
        # تدريب النموذج
        model_info, results = train_advanced_weather_model()
        
        print("\n" + "=" * 50)
        print("✅ تم الانتهاء من التدريب بنجاح!")
        print(f"🏆 أفضل نموذج: {model_info['model_name']}")
        print(f"🎯 دقة النموذج: {model_info['performance']['r2']:.4f}")
        print(f"📊 متوسط الخطأ المطلق: {model_info['performance']['mae']:.2f}°C")
        
        print("\n📋 ملخص جميع النماذج:")
        print("-" * 30)
        for name, result in results.items():
            print(f"{name}: R² = {result['r2']:.4f}")
        
        print("\n🎉 يمكنك الآن تشغيل التطبيق باستخدام:")
        print("streamlit run app.py")
        
    except Exception as e:
        print(f"❌ خطأ في التدريب: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
