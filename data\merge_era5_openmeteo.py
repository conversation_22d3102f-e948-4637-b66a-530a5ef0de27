import xarray as xr
import pandas as pd

# دمج بيانات ERA5 (NetCDF) مع بيانات Open-Meteo (CSV)
def merge_era5_openmeteo(netcdf_path, csv_path, output_path):
    # قراءة بيانات ERA5
    ds = xr.open_dataset(netcdf_path)
    # استخراج بيانات أساسية (مثال: درجة الحرارة، الأمطار، إلخ)
    era5_df = ds[['t2m', 'tp', 'd2m', 'u10', 'v10']].to_dataframe().reset_index()
    era5_df['t2m'] = era5_df['t2m'] - 273.15  # تحويل الحرارة إلى مئوية
    # قراءة بيانات Open-Meteo
    meteo_df = pd.read_csv(csv_path)
    # دمج البيانات حسب الوقت (تحتاج مطابقة الأعمدة الزمنية)
    merged = pd.merge_asof(meteo_df.sort_values('time'), era5_df.sort_values('time'), on='time')
    merged.to_csv(output_path, index=False)
    print(f'تم دمج البيانات وحفظها في {output_path}')

if __name__ == "__main__":
    # مثال للاستخدام
    merge_era5_openmeteo('era5_sennar_may2024.nc', 'weather_data.csv', 'merged_weather_data.csv')
